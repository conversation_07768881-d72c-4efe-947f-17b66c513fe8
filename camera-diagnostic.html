<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخیص مشکل دوربین - Camera Diagnostic</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            direction: rtl;
            padding: 20px;
        }

        .diagnostic-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-section h3 {
            margin-bottom: 15px;
            color: #fff;
            font-size: 1.3rem;
        }

        .test-result {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }

        .test-result.success {
            background: rgba(40, 167, 69, 0.3);
            border: 1px solid #28a745;
            color: #90ee90;
        }

        .test-result.error {
            background: rgba(220, 53, 69, 0.3);
            border: 1px solid #dc3545;
            color: #ffb3b3;
        }

        .test-result.warning {
            background: rgba(255, 193, 7, 0.3);
            border: 1px solid #ffc107;
            color: #fff3cd;
        }

        .test-result.info {
            background: rgba(23, 162, 184, 0.3);
            border: 1px solid #17a2b8;
            color: #b8daff;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
            color: white;
        }

        .btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .device-list {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }

        .device-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #28a745;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }

        .video-test {
            background: #000;
            border-radius: 10px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
        }

        #testVideo {
            width: 100%;
            height: auto;
            max-height: 300px;
            border-radius: 10px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <h1>🔍 تشخیص مشکل دوربین</h1>
        
        <div class="test-section">
            <h3>🌐 بررسی پشتیبانی مرورگر</h3>
            <div id="browserSupport"></div>
            <button class="btn primary" onclick="checkBrowserSupport()">شروع تست</button>
        </div>

        <div class="test-section">
            <h3>📹 تشخیص دوربین‌ها</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="deviceProgress"></div>
            </div>
            <div id="deviceResults"></div>
            <button class="btn primary" onclick="detectCameras()">جستجوی دوربین</button>
        </div>

        <div class="test-section">
            <h3>🔒 تست مجوزها</h3>
            <div id="permissionResults"></div>
            <button class="btn primary" onclick="testPermissions()">تست مجوز</button>
        </div>

        <div class="test-section">
            <h3>📺 تست اتصال ویدیو</h3>
            <div class="video-test">
                <video id="testVideo" autoplay muted playsinline style="display: none;"></video>
                <div id="videoPlaceholder">آماده تست ویدیو</div>
            </div>
            <div id="videoResults"></div>
            <button class="btn success" onclick="testVideoConnection()">تست ویدیو</button>
            <button class="btn primary" onclick="stopVideoTest()">توقف تست</button>
        </div>

        <div class="test-section">
            <h3>📊 گزارش نهایی</h3>
            <div id="finalReport"></div>
            <button class="btn success" onclick="generateReport()">تولید گزارش</button>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn primary" onclick="window.open('camera-app.html', '_blank')">
                🎥 بازگشت به اپلیکیشن دوربین
            </button>
        </div>
    </div>

    <script>
        let testResults = {
            browserSupport: false,
            devicesFound: 0,
            permissionGranted: false,
            videoWorking: false,
            errors: []
        };

        function updateProgress(percentage) {
            document.getElementById('deviceProgress').style.width = percentage + '%';
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        function checkBrowserSupport() {
            const container = document.getElementById('browserSupport');
            container.innerHTML = '';

            // Check MediaDevices API
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                addResult('browserSupport', 'success', '✅ مرورگر از دوربین پشتیبانی می‌کند');
                testResults.browserSupport = true;
            } else {
                addResult('browserSupport', 'error', '❌ مرورگر از دوربین پشتیبانی نمی‌کند');
                testResults.errors.push('Browser not supported');
            }

            // Check HTTPS
            if (location.protocol === 'https:' || location.hostname === 'localhost') {
                addResult('browserSupport', 'success', '✅ اتصال امن (HTTPS/localhost)');
            } else {
                addResult('browserSupport', 'warning', '⚠️ توصیه: از HTTPS استفاده کنید');
            }

            // Check browser type
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome')) {
                addResult('browserSupport', 'success', '✅ مرورگر Chrome - پشتیبانی کامل');
            } else if (userAgent.includes('Firefox')) {
                addResult('browserSupport', 'success', '✅ مرورگر Firefox - پشتیبانی کامل');
            } else if (userAgent.includes('Safari')) {
                addResult('browserSupport', 'warning', '⚠️ مرورگر Safari - ممکن است محدودیت داشته باشد');
            } else {
                addResult('browserSupport', 'warning', '⚠️ مرورگر نامشخص - ممکن است مشکل داشته باشد');
            }
        }

        async function detectCameras() {
            const container = document.getElementById('deviceResults');
            container.innerHTML = '<div class="loading"></div> در حال جستجو...';
            updateProgress(0);

            try {
                updateProgress(25);
                
                // First get permission
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                stream.getTracks().forEach(track => track.stop());
                
                updateProgress(50);
                
                // Then enumerate devices
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                
                updateProgress(75);
                
                container.innerHTML = '';
                
                if (videoDevices.length > 0) {
                    addResult('deviceResults', 'success', `✅ ${videoDevices.length} دوربین یافت شد`);
                    testResults.devicesFound = videoDevices.length;
                    
                    const deviceList = document.createElement('div');
                    deviceList.className = 'device-list';
                    deviceList.innerHTML = '<strong>دوربین‌های یافت شده:</strong>';
                    
                    videoDevices.forEach((device, index) => {
                        const deviceItem = document.createElement('div');
                        deviceItem.className = 'device-item';
                        deviceItem.innerHTML = `
                            📹 ${device.label || `دوربین ${index + 1}`}<br>
                            <small>ID: ${device.deviceId.substring(0, 20)}...</small>
                        `;
                        deviceList.appendChild(deviceItem);
                    });
                    
                    container.appendChild(deviceList);
                } else {
                    addResult('deviceResults', 'error', '❌ هیچ دوربینی یافت نشد');
                    testResults.errors.push('No cameras found');
                }
                
                updateProgress(100);
                
            } catch (error) {
                container.innerHTML = '';
                addResult('deviceResults', 'error', `❌ خطا در تشخیص: ${error.message}`);
                testResults.errors.push(`Detection error: ${error.message}`);
                updateProgress(0);
            }
        }

        async function testPermissions() {
            const container = document.getElementById('permissionResults');
            container.innerHTML = '<div class="loading"></div> تست مجوزها...';

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
                
                addResult('permissionResults', 'success', '✅ مجوز دوربین دریافت شد');
                testResults.permissionGranted = true;
                
                // Test video track
                const videoTrack = stream.getVideoTracks()[0];
                if (videoTrack) {
                    const settings = videoTrack.getSettings();
                    addResult('permissionResults', 'info', `📐 رزولوشن: ${settings.width}×${settings.height}`);
                    addResult('permissionResults', 'info', `🎬 نرخ فریم: ${settings.frameRate} FPS`);
                }
                
                stream.getTracks().forEach(track => track.stop());
                
            } catch (error) {
                if (error.name === 'NotAllowedError') {
                    addResult('permissionResults', 'error', '❌ مجوز دسترسی رد شد');
                } else if (error.name === 'NotFoundError') {
                    addResult('permissionResults', 'error', '❌ دوربین یافت نشد');
                } else if (error.name === 'NotReadableError') {
                    addResult('permissionResults', 'error', '❌ دوربین در حال استفاده است');
                } else {
                    addResult('permissionResults', 'error', `❌ خطا: ${error.message}`);
                }
                testResults.errors.push(`Permission error: ${error.message}`);
            }
        }

        async function testVideoConnection() {
            const container = document.getElementById('videoResults');
            const video = document.getElementById('testVideo');
            const placeholder = document.getElementById('videoPlaceholder');
            
            container.innerHTML = '<div class="loading"></div> تست اتصال ویدیو...';

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 640 }, 
                        height: { ideal: 480 } 
                    } 
                });
                
                video.srcObject = stream;
                video.style.display = 'block';
                placeholder.style.display = 'none';
                
                await video.play();
                
                container.innerHTML = '';
                addResult('videoResults', 'success', '✅ ویدیو با موفقیت شروع شد');
                addResult('videoResults', 'success', '✅ تصویر زنده در حال نمایش');
                testResults.videoWorking = true;
                
                // Monitor video for a few seconds
                setTimeout(() => {
                    if (video.videoWidth > 0 && video.videoHeight > 0) {
                        addResult('videoResults', 'success', `✅ ابعاد ویدیو: ${video.videoWidth}×${video.videoHeight}`);
                    }
                }, 2000);
                
            } catch (error) {
                container.innerHTML = '';
                addResult('videoResults', 'error', `❌ خطا در ویدیو: ${error.message}`);
                testResults.errors.push(`Video error: ${error.message}`);
                video.style.display = 'none';
                placeholder.style.display = 'block';
                placeholder.textContent = 'خطا در نمایش ویدیو';
            }
        }

        function stopVideoTest() {
            const video = document.getElementById('testVideo');
            const placeholder = document.getElementById('videoPlaceholder');
            
            if (video.srcObject) {
                video.srcObject.getTracks().forEach(track => track.stop());
                video.srcObject = null;
            }
            
            video.style.display = 'none';
            placeholder.style.display = 'block';
            placeholder.textContent = 'تست ویدیو متوقف شد';
            
            addResult('videoResults', 'info', '⏹️ تست ویدیو متوقف شد');
        }

        function generateReport() {
            const container = document.getElementById('finalReport');
            container.innerHTML = '';
            
            let score = 0;
            let maxScore = 4;
            
            if (testResults.browserSupport) score++;
            if (testResults.devicesFound > 0) score++;
            if (testResults.permissionGranted) score++;
            if (testResults.videoWorking) score++;
            
            const percentage = Math.round((score / maxScore) * 100);
            
            if (percentage >= 75) {
                addResult('finalReport', 'success', `🎉 عالی! امتیاز: ${score}/${maxScore} (${percentage}%)`);
                addResult('finalReport', 'success', '✅ دوربین شما آماده استفاده است');
            } else if (percentage >= 50) {
                addResult('finalReport', 'warning', `⚠️ متوسط! امتیاز: ${score}/${maxScore} (${percentage}%)`);
                addResult('finalReport', 'warning', '🔧 ممکن است نیاز به تنظیمات داشته باشید');
            } else {
                addResult('finalReport', 'error', `❌ ضعیف! امتیاز: ${score}/${maxScore} (${percentage}%)`);
                addResult('finalReport', 'error', '🛠️ نیاز به عیب‌یابی دارید');
            }
            
            // Show detailed results
            addResult('finalReport', 'info', `📊 تعداد دوربین‌ها: ${testResults.devicesFound}`);
            addResult('finalReport', 'info', `🔒 مجوز: ${testResults.permissionGranted ? 'دریافت شده' : 'رد شده'}`);
            addResult('finalReport', 'info', `📺 ویدیو: ${testResults.videoWorking ? 'کار می‌کند' : 'مشکل دارد'}`);
            
            if (testResults.errors.length > 0) {
                addResult('finalReport', 'error', `❌ خطاها: ${testResults.errors.length} مورد`);
            }
        }

        // Auto-start browser support check
        window.onload = function() {
            checkBrowserSupport();
        };
    </script>
</body>
</html>
