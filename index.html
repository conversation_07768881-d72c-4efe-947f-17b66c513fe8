<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظیمات دوربین - Camera Settings</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎥 تنظیمات دوربین</h1>
            <p>اتصال و پیکربندی کامل دوربین</p>
        </header>

        <div class="main-content">
            <!-- Camera Connection Section -->
            <div class="section">
                <h2>🔗 اتصال دوربین</h2>
                <div class="connection-panel">
                    <div class="status-indicator" id="connectionStatus">
                        <span class="status-dot offline"></span>
                        <span>غیرفعال</span>
                    </div>
                    
                    <div class="connection-methods">
                        <button id="connectWebcam" class="btn primary">
                            📹 اتصال وب‌کم
                        </button>
                        <button id="connectIP" class="btn secondary">
                            🌐 اتصال IP Camera
                        </button>
                        <button id="scanQR" class="btn secondary">
                            📱 اسکن QR Code
                        </button>
                    </div>

                    <div class="ip-input" id="ipInputSection" style="display: none;">
                        <input type="text" id="ipAddress" placeholder="*************:8080" />
                        <input type="text" id="username" placeholder="نام کاربری" />
                        <input type="password" id="password" placeholder="رمز عبور" />
                        <button id="connectIPBtn" class="btn primary">اتصال</button>
                    </div>
                </div>
            </div>

            <!-- Camera Preview Section -->
            <div class="section">
                <h2>👁️ پیش‌نمایش دوربین</h2>
                <div class="camera-preview">
                    <video id="cameraVideo" autoplay muted></video>
                    <canvas id="cameraCanvas" style="display: none;"></canvas>
                    <div class="preview-overlay">
                        <div class="recording-indicator" id="recordingIndicator" style="display: none;">
                            🔴 در حال ضبط
                        </div>
                    </div>
                </div>
                
                <div class="camera-controls">
                    <button id="takePhoto" class="btn primary">📸 عکس</button>
                    <button id="startRecord" class="btn secondary">🎬 شروع ضبط</button>
                    <button id="stopRecord" class="btn danger" style="display: none;">⏹️ توقف ضبط</button>
                    <button id="fullscreen" class="btn secondary">🔍 تمام صفحه</button>
                </div>
            </div>

            <!-- Camera Settings Section -->
            <div class="section">
                <h2>⚙️ تنظیمات دوربین</h2>
                <div class="settings-grid">
                    <div class="setting-group">
                        <label>کیفیت تصویر</label>
                        <select id="resolution">
                            <option value="1920x1080">1080p (1920×1080)</option>
                            <option value="1280x720">720p (1280×720)</option>
                            <option value="640x480">480p (640×480)</option>
                            <option value="320x240">240p (320×240)</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label>نرخ فریم</label>
                        <select id="framerate">
                            <option value="60">60 FPS</option>
                            <option value="30" selected>30 FPS</option>
                            <option value="24">24 FPS</option>
                            <option value="15">15 FPS</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label>روشنایی</label>
                        <input type="range" id="brightness" min="0" max="100" value="50">
                        <span id="brightnessValue">50</span>
                    </div>

                    <div class="setting-group">
                        <label>کنتراست</label>
                        <input type="range" id="contrast" min="0" max="100" value="50">
                        <span id="contrastValue">50</span>
                    </div>

                    <div class="setting-group">
                        <label>اشباع رنگ</label>
                        <input type="range" id="saturation" min="0" max="100" value="50">
                        <span id="saturationValue">50</span>
                    </div>

                    <div class="setting-group">
                        <label>تیزی</label>
                        <input type="range" id="sharpness" min="0" max="100" value="50">
                        <span id="sharpnessValue">50</span>
                    </div>
                </div>

                <div class="advanced-settings">
                    <h3>تنظیمات پیشرفته</h3>
                    <div class="checkbox-group">
                        <label>
                            <input type="checkbox" id="autoFocus" checked>
                            فوکوس خودکار
                        </label>
                        <label>
                            <input type="checkbox" id="autoExposure" checked>
                            نوردهی خودکار
                        </label>
                        <label>
                            <input type="checkbox" id="nightMode">
                            حالت شب
                        </label>
                        <label>
                            <input type="checkbox" id="motionDetection">
                            تشخیص حرکت
                        </label>
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="resetSettings" class="btn secondary">🔄 بازنشانی</button>
                    <button id="saveSettings" class="btn primary">💾 ذخیره تنظیمات</button>
                    <button id="exportSettings" class="btn secondary">📤 صادرات تنظیمات</button>
                </div>
            </div>

            <!-- QR Scanner Section -->
            <div class="section" id="qrSection" style="display: none;">
                <h2>📱 اسکنر QR Code</h2>
                <div class="qr-scanner">
                    <video id="qrVideo" autoplay></video>
                    <div class="qr-overlay">
                        <div class="qr-frame"></div>
                    </div>
                </div>
                <button id="closeQR" class="btn secondary">بستن</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
