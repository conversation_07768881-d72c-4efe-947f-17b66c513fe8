<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اپلیکیشن دوربین - Camera App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: white;
            direction: rtl;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .app-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .app-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .camera-main {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }

        .camera-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .status-light {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ff4444;
            animation: pulse 2s infinite;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
        }

        .status-light.connected {
            background: #44ff44;
            box-shadow: 0 0 10px rgba(68, 255, 68, 0.5);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 20px;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #mainVideo {
            width: 100%;
            height: auto;
            max-height: 500px;
            object-fit: cover;
            border-radius: 15px;
        }

        .video-overlay {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            pointer-events: none;
        }

        .video-info {
            background: rgba(0, 0, 0, 0.7);
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            color: white;
        }

        .no-camera {
            text-align: center;
            color: #ccc;
            font-size: 1.2rem;
        }

        .camera-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
        }

        .control-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .control-btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        .control-btn.secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .camera-list {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .camera-list h3 {
            margin-bottom: 15px;
            color: #fff;
        }

        .camera-device {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .camera-device:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .camera-device.active {
            border-color: #44ff44;
            background: rgba(68, 255, 68, 0.1);
        }

        .device-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .device-id {
            font-size: 12px;
            opacity: 0.7;
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .resolution-selector {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .resolution-selector h3 {
            margin-bottom: 15px;
            color: #fff;
        }

        .resolution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .resolution-btn {
            padding: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 14px;
        }

        .resolution-btn:hover {
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
        }

        .resolution-btn.active {
            border-color: #44ff44;
            background: rgba(68, 255, 68, 0.2);
        }

        @media (max-width: 768px) {
            .app-container {
                padding: 10px;
            }
            
            .camera-controls {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-header">
            <h1>🎥 اپلیکیشن دوربین</h1>
            <p>اتصال مستقیم و نمایش ویدیو دوربین</p>
        </div>

        <div class="camera-main">
            <div class="camera-status">
                <div class="status-light" id="statusLight"></div>
                <span id="statusText">در حال جستجوی دوربین...</span>
            </div>

            <div class="video-container">
                <video id="mainVideo" autoplay muted playsinline></video>
                <div class="video-overlay">
                    <div class="video-info" id="videoInfo">
                        آماده اتصال
                    </div>
                </div>
                <div class="no-camera" id="noCameraMsg" style="display: none;">
                    🚫 دوربین یافت نشد
                </div>
            </div>

            <div class="camera-controls">
                <button class="control-btn success" id="connectBtn">
                    🔗 اتصال دوربین
                </button>
                <button class="control-btn primary" id="takePhotoBtn" disabled>
                    📸 عکس
                </button>
                <button class="control-btn danger" id="recordBtn" disabled>
                    🎬 ضبط
                </button>
                <button class="control-btn secondary" id="fullscreenBtn" disabled>
                    🔍 تمام صفحه
                </button>
                <button class="control-btn secondary" id="refreshBtn">
                    🔄 تازه‌سازی
                </button>
                <button class="control-btn secondary" id="settingsBtn">
                    ⚙️ تنظیمات
                </button>
            </div>
        </div>

        <div class="camera-list" id="cameraList">
            <h3>📹 دوربین‌های موجود</h3>
            <div id="devicesList">
                در حال جستجو...
            </div>
        </div>

        <div class="resolution-selector">
            <h3>📐 کیفیت ویدیو</h3>
            <div class="resolution-grid">
                <div class="resolution-btn" data-resolution="320x240">240p</div>
                <div class="resolution-btn" data-resolution="640x480">480p</div>
                <div class="resolution-btn active" data-resolution="1280x720">720p</div>
                <div class="resolution-btn" data-resolution="1920x1080">1080p</div>
                <div class="resolution-btn" data-resolution="2560x1440">1440p</div>
                <div class="resolution-btn" data-resolution="3840x2160">4K</div>
            </div>
        </div>

        <div class="quick-actions">
            <button class="control-btn primary" id="autoConnectBtn">
                🚀 اتصال خودکار
            </button>
            <button class="control-btn secondary" id="testCameraBtn">
                🧪 تست دوربین
            </button>
            <button class="control-btn secondary" id="downloadAppBtn">
                📱 دانلود اپ
            </button>
        </div>
    </div>

    <script>
        class CameraApp {
            constructor() {
                this.currentStream = null;
                this.mediaRecorder = null;
                this.isRecording = false;
                this.devices = [];
                this.currentDeviceId = null;
                this.currentResolution = { width: 1280, height: 720 };
                
                this.initializeElements();
                this.bindEvents();
                this.autoDetectCameras();
            }

            initializeElements() {
                this.video = document.getElementById('mainVideo');
                this.statusLight = document.getElementById('statusLight');
                this.statusText = document.getElementById('statusText');
                this.videoInfo = document.getElementById('videoInfo');
                this.noCameraMsg = document.getElementById('noCameraMsg');
                this.devicesList = document.getElementById('devicesList');
                
                this.connectBtn = document.getElementById('connectBtn');
                this.takePhotoBtn = document.getElementById('takePhotoBtn');
                this.recordBtn = document.getElementById('recordBtn');
                this.fullscreenBtn = document.getElementById('fullscreenBtn');
                this.refreshBtn = document.getElementById('refreshBtn');
                this.settingsBtn = document.getElementById('settingsBtn');
                this.autoConnectBtn = document.getElementById('autoConnectBtn');
                this.testCameraBtn = document.getElementById('testCameraBtn');
            }

            bindEvents() {
                this.connectBtn.addEventListener('click', () => this.connectToCamera());
                this.takePhotoBtn.addEventListener('click', () => this.takePhoto());
                this.recordBtn.addEventListener('click', () => this.toggleRecording());
                this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
                this.refreshBtn.addEventListener('click', () => this.refreshCameras());
                this.autoConnectBtn.addEventListener('click', () => this.autoConnect());
                this.testCameraBtn.addEventListener('click', () => this.testCamera());

                // Resolution buttons
                document.querySelectorAll('.resolution-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => this.changeResolution(e.target));
                });
            }

            async autoDetectCameras() {
                try {
                    this.updateStatus('در حال جستجوی دوربین‌ها...', false);
                    
                    // Request permission first
                    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    stream.getTracks().forEach(track => track.stop());
                    
                    // Get available devices
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    this.devices = devices.filter(device => device.kind === 'videoinput');
                    
                    this.displayDevices();
                    
                    if (this.devices.length > 0) {
                        this.updateStatus(`${this.devices.length} دوربین یافت شد`, false);
                        this.connectBtn.textContent = '🔗 اتصال به دوربین';
                        this.connectBtn.disabled = false;
                    } else {
                        this.updateStatus('هیچ دوربینی یافت نشد', false);
                        this.noCameraMsg.style.display = 'block';
                    }
                } catch (error) {
                    console.error('خطا در تشخیص دوربین:', error);
                    this.updateStatus('خطا در دسترسی به دوربین', false);
                    this.noCameraMsg.style.display = 'block';
                }
            }

            displayDevices() {
                if (this.devices.length === 0) {
                    this.devicesList.innerHTML = '<div style="text-align: center; color: #ccc;">هیچ دوربینی یافت نشد</div>';
                    return;
                }

                this.devicesList.innerHTML = '';
                this.devices.forEach((device, index) => {
                    const deviceElement = document.createElement('div');
                    deviceElement.className = 'camera-device';
                    deviceElement.innerHTML = `
                        <div class="device-name">📹 ${device.label || `دوربین ${index + 1}`}</div>
                        <div class="device-id">ID: ${device.deviceId.substring(0, 20)}...</div>
                    `;
                    
                    deviceElement.addEventListener('click', () => {
                        this.selectDevice(device.deviceId, deviceElement);
                    });
                    
                    this.devicesList.appendChild(deviceElement);
                });
            }

            selectDevice(deviceId, element) {
                // Remove active class from all devices
                document.querySelectorAll('.camera-device').forEach(el => {
                    el.classList.remove('active');
                });
                
                // Add active class to selected device
                element.classList.add('active');
                this.currentDeviceId = deviceId;
                
                this.updateStatus('دوربین انتخاب شد - آماده اتصال', false);
            }

            async connectToCamera() {
                try {
                    this.updateStatus('در حال اتصال...', false);
                    
                    const constraints = {
                        video: {
                            deviceId: this.currentDeviceId ? { exact: this.currentDeviceId } : undefined,
                            width: { ideal: this.currentResolution.width },
                            height: { ideal: this.currentResolution.height },
                            frameRate: { ideal: 30 }
                        },
                        audio: false
                    };

                    // Stop current stream if exists
                    if (this.currentStream) {
                        this.currentStream.getTracks().forEach(track => track.stop());
                    }

                    this.currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                    this.video.srcObject = this.currentStream;
                    
                    // Wait for video to load
                    await new Promise((resolve) => {
                        this.video.onloadedmetadata = resolve;
                    });

                    this.updateStatus('دوربین متصل شد', true);
                    this.enableControls(true);
                    this.updateVideoInfo();
                    this.noCameraMsg.style.display = 'none';
                    
                    console.log('دوربین با موفقیت متصل شد');
                } catch (error) {
                    console.error('خطا در اتصال دوربین:', error);
                    this.updateStatus('خطا در اتصال دوربین', false);
                    alert('خطا در اتصال به دوربین. لطفاً مجوزهای دوربین را بررسی کنید.');
                }
            }

            async autoConnect() {
                this.updateStatus('اتصال خودکار...', false);
                
                if (this.devices.length > 0) {
                    // Select first available camera
                    this.currentDeviceId = this.devices[0].deviceId;
                    
                    // Update UI
                    const firstDevice = document.querySelector('.camera-device');
                    if (firstDevice) {
                        firstDevice.classList.add('active');
                    }
                    
                    // Connect automatically
                    await this.connectToCamera();
                } else {
                    await this.autoDetectCameras();
                    if (this.devices.length > 0) {
                        setTimeout(() => this.autoConnect(), 1000);
                    }
                }
            }

            updateStatus(message, connected) {
                this.statusText.textContent = message;
                this.statusLight.className = `status-light ${connected ? 'connected' : ''}`;
            }

            enableControls(enabled) {
                this.takePhotoBtn.disabled = !enabled;
                this.recordBtn.disabled = !enabled;
                this.fullscreenBtn.disabled = !enabled;
                
                if (enabled) {
                    this.connectBtn.textContent = '🔄 تغییر دوربین';
                } else {
                    this.connectBtn.textContent = '🔗 اتصال دوربین';
                }
            }

            updateVideoInfo() {
                if (this.video.videoWidth && this.video.videoHeight) {
                    this.videoInfo.textContent = `${this.video.videoWidth}×${this.video.videoHeight} | 30 FPS`;
                }
            }

            takePhoto() {
                if (!this.currentStream) return;

                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                
                canvas.width = this.video.videoWidth;
                canvas.height = this.video.videoHeight;
                
                context.drawImage(this.video, 0, 0);
                
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `camera_photo_${new Date().getTime()}.png`;
                    a.click();
                    URL.revokeObjectURL(url);
                });
                
                // Visual feedback
                this.video.style.filter = 'brightness(1.5)';
                setTimeout(() => {
                    this.video.style.filter = '';
                }, 200);
                
                console.log('عکس گرفته شد');
            }

            toggleRecording() {
                if (!this.isRecording) {
                    this.startRecording();
                } else {
                    this.stopRecording();
                }
            }

            async startRecording() {
                try {
                    this.mediaRecorder = new MediaRecorder(this.currentStream);
                    const chunks = [];
                    
                    this.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            chunks.push(event.data);
                        }
                    };
                    
                    this.mediaRecorder.onstop = () => {
                        const blob = new Blob(chunks, { type: 'video/webm' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `camera_video_${new Date().getTime()}.webm`;
                        a.click();
                        URL.revokeObjectURL(url);
                    };
                    
                    this.mediaRecorder.start();
                    this.isRecording = true;
                    
                    this.recordBtn.textContent = '⏹️ توقف';
                    this.recordBtn.className = 'control-btn danger';
                    this.updateStatus('در حال ضبط...', true);
                    
                } catch (error) {
                    console.error('خطا در شروع ضبط:', error);
                    alert('خطا در شروع ضبط ویدیو');
                }
            }

            stopRecording() {
                if (this.mediaRecorder && this.isRecording) {
                    this.mediaRecorder.stop();
                    this.isRecording = false;
                    
                    this.recordBtn.textContent = '🎬 ضبط';
                    this.recordBtn.className = 'control-btn danger';
                    this.updateStatus('دوربین متصل شد', true);
                }
            }

            toggleFullscreen() {
                if (!document.fullscreenElement) {
                    this.video.requestFullscreen().catch(err => {
                        console.error('خطا در تمام صفحه:', err);
                    });
                } else {
                    document.exitFullscreen();
                }
            }

            async refreshCameras() {
                this.updateStatus('در حال تازه‌سازی...', false);
                await this.autoDetectCameras();
            }

            changeResolution(button) {
                // Remove active class from all resolution buttons
                document.querySelectorAll('.resolution-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                // Add active class to clicked button
                button.classList.add('active');
                
                // Parse resolution
                const resolution = button.dataset.resolution.split('x');
                this.currentResolution = {
                    width: parseInt(resolution[0]),
                    height: parseInt(resolution[1])
                };
                
                // Reconnect with new resolution if camera is connected
                if (this.currentStream) {
                    this.connectToCamera();
                }
            }

            async testCamera() {
                this.updateStatus('تست دوربین...', false);
                
                try {
                    // Test basic camera access
                    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    stream.getTracks().forEach(track => track.stop());
                    
                    alert('✅ دوربین سالم است و آماده استفاده');
                    this.updateStatus('تست موفق - دوربین سالم', false);
                } catch (error) {
                    alert('❌ مشکل در دسترسی به دوربین');
                    this.updateStatus('تست ناموفق', false);
                }
            }
        }

        // Initialize camera app when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.cameraApp = new CameraApp();
            console.log('اپلیکیشن دوربین آماده است');
        });
    </script>
</body>
</html>
